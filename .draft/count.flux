import "date"
queryBegin = time(v: 2025-07-04T10:40:56+08:00)
queryEnd = time(v: 2025-07-11T10:40:56+08:00)
nowTime = now()
nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd
nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin
t0 = from(bucket:"nlink-test")
|> range(start: 0, stop: queryBegin)
|> filter(fn: (r) => r["_measurement"] == "1010203040506070809" and r["deviceId"] == "1433776120848384")
|> filter(fn: (r) => r["property"] == "State")
|> sort(columns:["_time"])
|> last(column: "_value")
|> map(fn: (r) => ({ r with _time: nowOrBegin }))

t1 = from(bucket:"nlink-test")
|> range(start: queryBegin, stop: queryEnd)
|> filter(fn: (r) => r["_measurement"] == "1010203040506070809" and r["deviceId"] == "1433776120848384")
|> filter(fn: (r) => r["property"] == "State")
union(tables: [t0, t1])
|> sort(columns:["_time"])
|> map(fn: (r) => ({ r with
    duration: if exists r.duration then r.duration else 0,
    time_to_stop: if exists r.time_to_stop then r.time_to_stop else 0
}))
|> map(fn: (r) => ({ r with 
    _time: date.truncate(t: r._time, unit: 1d),
    effectiveDuration: if r.duration > 86400 then r.time_to_stop 
                      else if r.duration < r.time_to_stop then r.duration 
                      else r.time_to_stop
}))
|> filter(fn: (r) => r["_value"] == 3)
|> filter(fn: (r) => exists r.effectiveDuration and r.effectiveDuration >= 0)
// |> group(columns: ["deviceId", "_time"])
// |> reduce(
//     fn: (r, accumulator) => ({
//         deviceId: r.deviceId,
//         _time: r._time,
//         faultCount: accumulator.faultCount + 1,
//     }),
//     identity: {
//         deviceId: "",
//         _time: 2021-01-01T00:00:00Z,
//         faultCount: 0,
//     }
// )
// |>group()