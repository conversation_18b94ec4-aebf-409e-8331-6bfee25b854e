queryBegin = time(v: 2025-07-04T10:16:28+08:00)
queryEnd = time(v: 2025-07-11T11:16:28+08:00)
nowTime = now()
nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd
nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin
t0 = from(bucket:"nlink-test")
|> range(start: 0, stop: queryBegin)
|> filter(fn: (r) => r["_measurement"] == "1010203040506070809" and ( r["deviceId"] =~ /1433775824257024|1433775824297984|1433776120848384|1419063418978304/ ) )
|> filter(fn: (r) => r["property"] == "State")
|> keep(columns: ["_time","deviceId","property", "_value"])
|> sort(columns:["_time"])
|> last(column: "_value")
|> map(fn: (r) => ({ r with _time: nowOrBegin })) 

t1 = from(bucket:"nlink-test")
|> range(start: queryBegin, stop: queryEnd)
|> filter(fn: (r) => r["_measurement"] == "1010203040506070809" and ( r["deviceId"] =~ /1433775824257024|1433775824297984|1433776120848384|1419063418978304/ ) )
|> filter(fn: (r) => r["property"] == "State")
|> keep(columns: ["_time","deviceId","property", "_value"])
t2 = 
from(bucket: "nlink-test")
|> range(start: queryBegin, stop: queryEnd)
|> filter(fn: (r) => r["_measurement"] == "1010203040506070809" and ( r["deviceId"] =~ /1433775824257024|1433775824297984|1433776120848384|1419063418978304/ ) )
|> filter(fn: (r) => r["property"] == "State")
|> keep(columns: ["_time","deviceId","property", "_value"])
|> sort(columns:["_time"])
|> last(column: "_value")
|> map(fn: (r) => ({ r with _time: nowOrEnd  })) 
union(tables: [t0, t1])
|> sort(columns:["_time"])
|> filter(fn: (r) => r["_value"] == 3)
// |> group(columns: ["deviceId"])
// |> count(column: "_value")
// |> map(fn: (r) => ({ r with dur: r["_value"] }))
// |> drop(columns: ["_value"])
// |> sort(columns: ["dur"], desc: true)
// |> limit(n: 10)