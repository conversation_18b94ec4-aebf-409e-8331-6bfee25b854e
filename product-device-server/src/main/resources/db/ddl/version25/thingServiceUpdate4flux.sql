-- 更新第二条记录的service_code
UPDATE `thing_service`
SET `service_code` = 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n\'import \"date\"\' + \"\\n\" +\n\'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n\'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n\'nowTime = now()\' + \"\\n\" +\n\'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n\'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\'t0 = \' + \"\\n\" +\n\'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n\'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n\'|> filter(fn: (r) => \' + idsStr + \')\' + \"\\n\" +\n\'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n\'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\'|> last(column: \"_value\")\' + \"\\n\" +\n\'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\'t1 = \' + \"\\n\" +\n\'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n\'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n\'|> filter(fn: (r) => \' + idsStr + \')\' + \"\\n\" +\n\'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n\'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\'t2 = \' + \"\\n\" +\n\'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n\'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n\'|> filter(fn: (r) => \' + idsStr + \')\' + \"\\n\" +\n\'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n\'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\'|> last(column: \"_value\")\' + \"\\n\" +\n\'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n\'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\'t3 =\' + \"\\n\" +\n\'union(tables: [t0, t1, t2])\' + \"\\n\" +\n\'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\'t4 = t3\' + \"\\n\" +\n\'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\'|> window(every: 1d, offset: -8h)\' + \"\\n\" +\n\'|> last()\' + \"\\n\" +\n\'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_stop\"])  }))\' + \"\\n\" +\n\'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\'t5 = \' + \"\\n\" +\n\'union(tables: [t3, t4])\' + \"\\n\" +\n\'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\'t5\' + \"\\n\" +\n\'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n\'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n\'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n\'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n\'        if r.property == \"State_OnLine\" and r.val == 1 then \"offline\" \' + \"\\n\" +\n\'        else if r.property == \"State_OnLine\" and r.val == -1 then \"online\"\' + \"\\n\" +\n\'        else if r.property == \"State_OnLine\" and r.val == 0 then \' + \"\\n\" +\n\'            if r._value then \"online\" else \"offline\"\' + \"\\n\" +\n\'        else if r.property == \"State_Task\" and r.val == 1 then \"noTask\"\' + \"\\n\" +\n\'        else if r.property == \"State_Task\" and r.val == -1 then \"task\"\' + \"\\n\" +\n\'        else if r.property == \"State_Task\" and r.val == 0 then \' + \"\\n\" +\n\'            if r._value then \"task\" else \"noTask\"\' + \"\\n\" +\n\'        else \"unknow\"\' + \"\\n\" +\n\'    })\' + \"\\n\" +\n\')\' + \"\\n\" +\n\'|> keep(columns: [\"_time\", \"dur\", \"type\"])\' + \"\\n\" +\n\'|> filter(fn: (r) => \' + \"\\n\" +\n\'    r[\"type\"] == \"task\" \' + \"\\n\" +\n\'    or r[\"type\"] == \"online\" \' + \"\\n\" +\n\')\' + \"\\n\" +\n\'|> aggregateWindow(every: 1d, createEmpty: false, column: \"dur\", offset: -8h, fn: (column, tables=<-) => tables \' + \"\\n\" +\n\'|> reduce(\' + \"\\n\" +\n\'        fn: (r, accumulator) => ({\' + \"\\n\" +\n\'            taskSum: (if r.type == \"task\" then r.dur else 0) + accumulator.taskSum, \' + \"\\n\" +\n\'            onlineSum: (if r.type == \"online\" then r.dur else 0) + accumulator.onlineSum\' + \"\\n\" +\n\'        }),\' + \"\\n\" +\n\'        identity: {taskSum: 0, onlineSum: 0},\' + \"\\n\" +\n\'    )\' + \"\\n\" +\n\'    |> map(fn: (r) => ({ r with useRate: if r.onlineSum > 0 and r.onlineSum>r.taskSum then (float(v: r[\"taskSum\"])/float(v: r[\"onlineSum\"]))  else\' + \"\\n\" +\n\'                                        if  r.onlineSum > 0 and r.onlineSum<=r.taskSum then 1.0 else 0.0}))\' + \"\\n\" +\n\')\' + \"\\n\" +\n\'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_time\"])  }))\';\n\nvar r = me.queryData(q);\nreturn r;\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\n// var tmpArray = [];\n// for (var i = 0; i < idArray.length; i++) {\n//     tmpArray.push(\'r[\"deviceId\"] == \"\' + idArray[i] + \'\"\');\n// }\n// var idsStr = tmpArray.join(\' or \');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'// queryDevicesUseRateByWindow\' + \"\\n\" +\n    \'import \"date\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and ( \' + idsStr + \' ) )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" or r[\"property\"] == \"State_OnLine\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'t3 =\' + \"\\n\" +\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t4 = t3\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> window(every: \' + input.every + \', offset: -8h)\' + \"\\n\" +\n    \'|> last()\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_stop\"])  }))\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t5 = \' + \"\\n\" +\n    \'union(tables: [t3, t4])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t5\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_OnLine\" and r.val == 1 then \"offline\" \' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == -1 then \"online\"\' + \"\\n\" +\n    \'        else if r.property == \"State_OnLine\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"online\" else \"offline\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == 1 then \"noTask\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == -1 then \"task\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"task\" else \"noTask\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"task\" \' + \"\\n\" +\n    \'    or r[\"type\"] == \"online\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> aggregateWindow(every: \' + input.every + \', createEmpty: false, column: \"dur\", offset: -8h, fn: (column, tables=<-) => tables \' + \"\\n\" +\n    \'|> reduce(\' + \"\\n\" +\n    \'        fn: (r, accumulator) => ({\' + \"\\n\" +\n    \'            taskSum: (if r.type == \"task\" then r.dur else 0) + accumulator.taskSum, \' + \"\\n\" +\n    \'            onlineSum: (if r.type == \"online\" then r.dur else 0) + accumulator.onlineSum\' + \"\\n\" +\n    \'        }),\' + \"\\n\" +\n    \'        identity: {taskSum: 0, onlineSum: 0},\' + \"\\n\" +\n    \'    )\' + \"\\n\" +\n    \'    |> map(fn: (r) => ({ r with useRate: if r.onlineSum > 0 then (float(v: r[\"taskSum\"])/float(v: r[\"onlineSum\"]))  else 0.0 }))\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_time\"])  }))\';\nvar r = me.queryData(q);\nreturn r;'
WHERE `service_name` = 'queryDevicesUseRateByWindow' AND `thing_model_id` = 999;



UPDATE `thing_service` 
SET `service_code` = 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'import \"date\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + idsStr + \' )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_StandBy\" )\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + idsStr + \' )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_StandBy\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) =>  \' + idsStr + \' )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_StandBy\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'t3 =\' + \"\\n\" +\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t4 = t3\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> window(every: 1d, offset: -8h)\' + \"\\n\" +\n    \'|> last()\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_stop\"])  }))\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t5 = \' + \"\\n\" +\n    \'union(tables: [t3, t4])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t5\' + \"\\n\" +\n\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_StandBy\" and r.val == 1 then \"noStandBy\"\' + \"\\n\" +\n    \'        else if r.property == \"State_StandBy\" and r.val == -1 then \"standBy\"\' + \"\\n\" +\n    \'        else if r.property == \"State_StandBy\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"standBy\" else \"noStandBy\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"standBy\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> aggregateWindow(every: 1d, fn: sum, createEmpty: false, offset: -8h, column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with standBySum: r[\"dur\"]/\' + idArray.length + \'}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with standBySumSeconds: r[\"dur\"]/\' + idArray.length + \'}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with standBySumStr: string(v: duration( v: ( r[\"standBySumSeconds\"]*1000*1000*1000 ) )) }))\' + \"\\n\" +\n    \'|> drop(columns: [\"_start\",\"_stop\", \"dur\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_time\"])  }))\';\n\n\nvar r = me.queryData(q);\nreturn r;' 
WHERE `service_name` = 'queryDevicesStandByTimeByWindow' AND `thing_model_id` = 999;


UPDATE `thing_service` 
SET `service_code` = 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'import \"date\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + idsStr + \' )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\" )\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + idsStr + \' )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + idsStr + \' )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'t3 =\' + \"\\n\" +\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t4 = t3\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> window(every: 1d, offset: -8h)\' + \"\\n\" +\n    \'|> last()\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_stop\"])  }))\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t5 = \' + \"\\n\" +\n    \'union(tables: [t3, t4])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t5\' + \"\\n\" +\n\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Fault\" and r.val == 1 then \"noFault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == -1 then \"fault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"fault\" else \"noFault\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"fault\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> aggregateWindow(every: 1d, fn: sum, createEmpty: false, offset: -8h, column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultSum: r[\"dur\"]/\' + idArray.length + \'}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultSumSeconds: r[\"dur\"]/\' + idArray.length + \'}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultSumStr: string(v: duration( v: ( r[\"faultSumSeconds\"]*1000*1000*1000 ) )) }))\' + \"\\n\" +\n    \'|> drop(columns: [\"_start\",\"_stop\", \"dur\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_time\"])  }))\';\n\nvar r = me.queryData(q);\nreturn r;' 
WHERE `service_name` = 'queryDevicesFaultTimeByWindow' AND `thing_model_id` = 999;



UPDATE `thing_service` 
SET `service_code` = 'var ids = input.deviceIds;\nvar idArray = ids.split(\',\');\n\nvar idsStr = \'r[\"deviceId\"] =~ /\' + idArray.join(\'|\') + \'/\';\n\nvar q =\n    \'import \"date\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + idsStr + \' )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\" )\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + idsStr + \')\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + idsStr + \' )\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Task\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'t3 =\' + \"\\n\" +\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t4 = t3\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> window(every: 1d, offset: -8h)\' + \"\\n\" +\n    \'|> last()\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_stop\"])  }))\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t5 = \' + \"\\n\" +\n    \'union(tables: [t3, t4])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n\n    \'t5\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Task\" and r.val == 1 then \"noTask\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == -1 then \"task\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Task\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"task\" else \"noTask\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"task\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> aggregateWindow(every: 1d, fn: sum, createEmpty: false, offset: -8h, column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with taskSum: r[\"dur\"]/\' + idArray.length + \'}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with taskSumSeconds: r[\"dur\"]/\' + idArray.length + \'}))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with taskSumStr: string(v: duration( v: ( r[\"taskSumSeconds\"]*1000*1000*1000) )) }))\' + \"\\n\" +\n    \'|> drop(columns: [\"_start\",\"_stop\", \"dur\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_time\"])  }))\';\n\nvar r = me.queryData(q);\nreturn r;' 
WHERE `service_name` = 'queryDevicesTaskTimeByWindow' AND `thing_model_id` = 999;