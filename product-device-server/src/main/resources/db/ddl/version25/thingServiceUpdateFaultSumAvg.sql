UPDATE `thing_service` SET `service_code` = 'var q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'lastFromT1 = t1 \' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd }))\' + \"\\n\" +\n\n    \'fallbackFromT0 = t0\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd }))\' + \"\\n\" +\n\n    \'t2Result = union(tables: [lastFromT1, fallbackFromT0])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2Result])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Fault\" and r.val == 1 then \"noFault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == -1 then \"fault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"fault\" else \"noFault\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"fault\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> group(columns: [\"deviceId\",\"type\"])\' + \"\\n\" +\n    \'|> sum(column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with durSeconds: r[\"dur\"]} ))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with durStr: string(v: duration( v: ( r[\"durSeconds\"]*1000*1000*1000 ) )) }))\' + \"\\n\";\nvar r = me.queryData(q);\nreturn r;' WHERE `service_name` = 'queryFaultTimeSum' AND `thing_model_id` = 999;

UPDATE `nlink_product_device_test`.`thing_service` SET `service_code` = 'var q =\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'lastFromT1 = t1 \' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd }))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true }))\' + \"\\n\" +\n\n    \'fallbackFromT0 = t0\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd }))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true }))\' + \"\\n\" +\n\n    \'t2 = union(tables: [lastFromT1, fallbackFromT0])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Fault\" and r.val == 1 then \"noFault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == -1 then \"fault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"fault\" else \"noFault\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"fault\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> reduce(\' + \"\\n\" +\n    \'    fn: (r, accumulator) => ({\' + \"\\n\" +\n    \'        countValue: accumulator.countValue + 1, \' + \"\\n\" +\n    \'        sumValue: r.dur + accumulator.sumValue\' + \"\\n\" +\n    \'    }),\' + \"\\n\" +\n    \'    identity: {countValue: 0, sumValue: 0},\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultAvg: r[\"sumValue\"]/r[\"countValue\"] }))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultAvgSeconds: r[\"faultAvg\"]} ))\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultAvgStr: string(v: duration( v: ( r[\"faultAvgSeconds\"]*1000*1000*1000 ) )) }))\' + \"\\n\";\n\nvar r = me.queryData(q);\nreturn r;' WHERE `service_name` = 'queryFaultTimeAvg' AND `thing_model_id` = 999;


UPDATE `thing_service` SET `service_code` = 'var q =\n    \'import \"date\"\' + \"\\n\" +\n    \'queryBegin = time(v: \' + input.begin + \'+08:00)\' + \"\\n\" +\n    \'queryEnd = time(v: \' + input.end + \'+08:00)\' + \"\\n\" +\n    \'nowTime = now()\' + \"\\n\" +\n    \'nowOrEnd = if nowTime < queryEnd then nowTime else queryEnd\' + \"\\n\" +\n    \'nowOrBegin = if nowTime < queryBegin then nowTime else queryBegin\' + \"\\n\" +\n\n    \'t0 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: 0, stop: queryBegin)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrBegin  })) \' + \"\\n\" +\n\n    \'t1 = \' + \"\\n\" +\n    \'from(bucket:\"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n\n    \'t2 = \' + \"\\n\" +\n    \'from(bucket: \"\' + me.influxBucket + \'\")\' + \"\\n\" +\n    \'|> range(start: queryBegin, stop: queryEnd)\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"_measurement\"] == \"\' + me.tenantId + \'\" and r[\"deviceId\"] == \"\' + me.id + \'\")\' + \"\\n\" +\n    \'|> filter(fn: (r) => r[\"property\"] == \"State_Fault\")\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\",\"property\", \"_value\"])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> last(column: \"_value\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: nowOrEnd  })) \' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _value: if r[\"_value\"] then false else true })) \' + \"\\n\" +\n\n    \'union(tables: [t0, t1, t2])\' + \"\\n\" +\n    \'|> sort(columns:[\"_time\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with val: int(v: r[\"_value\"])  })) \' + \"\\n\" +\n    \'|> difference(columns: [\"val\"], keepFirst: true)\' + \"\\n\" +\n    \'|> elapsed(unit: 1s, columnName: \"dur\", timeColumn: \"_time\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with type: \' + \"\\n\" +\n    \'        if r.property == \"State_Fault\" and r.val == 1 then \"noFault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == -1 then \"fault\"\' + \"\\n\" +\n    \'        else if r.property == \"State_Fault\" and r.val == 0 then \' + \"\\n\" +\n    \'            if r._value then \"fault\" else \"noFault\"\' + \"\\n\" +\n    \'        else \"unknow\"\' + \"\\n\" +\n    \'    })\' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> keep(columns: [\"_time\",\"deviceId\", \"dur\", \"type\"])\' + \"\\n\" +\n    \'|> filter(fn: (r) => \' + \"\\n\" +\n    \'    r[\"type\"] == \"fault\" \' + \"\\n\" +\n    \')\' + \"\\n\" +\n    \'|> aggregateWindow(every: \' + input.every + \', fn: count, createEmpty: false, offset: -8h, column: \"dur\")\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with faultCount: r[\"dur\"] }))\' + \"\\n\" +\n    \'|> drop(columns: [\"_start\",\"_stop\", \"dur\"])\' + \"\\n\" +\n    \'|> map(fn: (r) => ({ r with _time: date.sub(d: 1s, from: r[\"_time\"])  }))\';\nvar r = me.queryData(q);\nreturn r;' WHERE `service_name` = 'queryFaultCountByWindow' AND `thing_model_id` = 999;