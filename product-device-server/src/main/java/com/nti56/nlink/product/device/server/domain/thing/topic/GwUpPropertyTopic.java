package com.nti56.nlink.product.device.server.domain.thing.topic;

import com.nti56.nlink.product.device.server.domain.thing.enumerate.MqttTopicEnum;

import lombok.Builder;
import lombok.Data;

/**
 * 类说明: 网关属性上报topic
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-07-21 10:35:41
 * @since JDK 1.8
 */
public class GwUpPropertyTopic {
    
    @Data
    @Builder
    public static class TopicInfo{
        private String tenantId;
        private String edgeGatewayId;
        private String deviceId;
        private String reportType;
    }
    
    public static TopicInfo parseTopic(String topic){
        String[] split = topic.split("/");
        String tenantId = split[2];
        String edgeGatewayId = split[3];
        String deviceId = split[4];
        String reportType = split[7];

        return TopicInfo.builder()
            .tenantId(tenantId)
            .edgeGatewayId(edgeGatewayId)
            .deviceId(deviceId)
            .reportType(reportType)
            .build();
    }

    /**
     * 创建属性上报订阅topic
     */
    public static String createSubscribeTopic(String group){
        return "$share/" + group + "/" + MqttTopicEnum.GW_UP.getPrefix() 
                + "+/+/+/thing/properties/#";
    }

    /**
     * 创建topic
     */
    public static String createTopic(Long tenantId, Long edgeGatewayId, Long deviceId){
        return MqttTopicEnum.GW_UP.getPrefix()
            + tenantId + "/"
            + edgeGatewayId + "/"
            + deviceId + "/"
            + "thing/properties/changeReport"
            ;
    }
    
}
