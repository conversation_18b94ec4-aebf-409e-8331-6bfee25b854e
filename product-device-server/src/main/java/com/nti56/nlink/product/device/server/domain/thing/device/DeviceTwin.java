package com.nti56.nlink.product.device.server.domain.thing.device;

import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Maps;
import com.nti56.nlink.common.thing.ThingDataTypeEnum;
import com.nti56.nlink.product.device.server.constant.SubscriptionOutTypeConstant;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.DeviceStatusEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.SubscriptionEventTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.SubscriptionFromEnum;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.EventData;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.Subscription;
import com.nti56.nlink.product.device.server.domain.thing.up.UpProp;
import com.nti56.nlink.product.device.server.domain.thing.up.UpProp.UpPropBuilder;

import com.nti56.nlink.product.device.server.entity.ComputeTaskEntity;
import com.nti56.nlink.product.device.server.listener.NoChangeKeyExpireListener;
import com.nti56.nlink.product.device.server.listener.NoChangeKeyExpireListener.NoChangeInfo;
import com.nti56.nlink.product.device.server.service.ITaskService;
import com.nti56.nlink.product.device.server.service.cache.SubscriptionOrderCache;
import com.nti56.nlink.product.device.server.service.impl.TaskServiceImpl;
import com.nti56.nlink.product.device.server.util.ApplicationContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;


/**
 * 说明：
 *
 * <AUTHOR>
 * @ClassName DeviceTwin
 * @date 2022/7/22 11:59
 * @Version 1.0
 */
@Slf4j
public class DeviceTwin {

    /**
     * property -> value
     */
    Map<String,Object> actual;

    Long deviceId;

    DeviceDataResource deviceDataResource;

    public DeviceTwin(DeviceDataResource deviceDataResource, Long deviceId){
        this.deviceId = deviceId;
        this.deviceDataResource = deviceDataResource;
        actual = deviceDataResource.getById(deviceId);
    }

    public Map<String,Object> getProperties(Collection<String> properties){
        Map<String, Object> obj = new HashMap<>();
        properties.forEach(property ->{
            if (actual.containsKey(property)) {
                obj.put(property,actual.get(property));
            }
        });
        return obj;
    }

    public static Map<String,Object> getProperties(DeviceDataResource deviceDataResource, Long deviceId, Collection<String> properties){
        return deviceDataResource.getProperties(deviceId,properties);
    }


    public Object getProperty(String property){
        if (actual.containsKey(property)) {
            return actual.get(property);
        }
        return null;
    }

    public Object getPropertyInCurrent(String property){
        return deviceDataResource.getProperty(deviceId,property);
    }

    public static Object getPropertyInCurrent(DeviceDataResource deviceDataResource, Long deviceId, String property){
        return deviceDataResource.getProperty(deviceId,property);
    }

    public Map<String,Object> getActual(){
        return actual;
    }

    public static Map<String,Object> getActualInCurrent(DeviceDataResource deviceDataResource, Long deviceId){
        return deviceDataResource.getById(deviceId);
    }

    private static boolean setProperty(DeviceDataResource deviceDataResource, Long deviceId, String property, Object value){
        return deviceDataResource.setProperty(deviceId, property, value);
    }

    public Boolean setProperties(Map<String,Object> properties,Long timestamp){
        return setProperties(deviceDataResource,deviceId,properties);
    }

    public static Boolean setProperties(DeviceDataResource deviceDataResource, Long deviceId, Map<String,Object> properties){
        log.debug("DeviceTwin,id:{},resource:{},setProperties:{}",deviceId,deviceDataResource,properties);
        return deviceDataResource.setProperties(deviceId, properties);
    }
    
    public static Object changeValueType(ThingDataTypeEnum dataType, Boolean isArray, Object value){
        if(value == null){
            return null;
        }
        if(isArray){
            List<Object> list = null;
            if(value.getClass().isArray()){
                list = Arrays.asList(value);
            }else {
                list = (List)value;
            }
            List<Object> changeList = new ArrayList<>();
            for(Object obj:list){
                changeList.add(changeValueType(dataType, obj));
            }
            return changeList;
        }else{
            return changeValueType(dataType, value);
        }
    }

    private static Object changeValueType(ThingDataTypeEnum dataType, Object value){
        try {
            switch (dataType) {
                case BOOLEAN:{
                    if(value instanceof String){
                        if("1".equals((String)value)){
                            return true;
                        }else if("0".equals((String)value)){
                            return false;
                        }else if("true".equals((String)value)){
                            return true;
                        }else if("false".equals((String)value)){
                            return false;
                        }
                        Boolean booleanValue = Boolean.valueOf((String)value);
                        return booleanValue;
                    }
                    break;
                }
                case LONG:
                case WORD: {
                    if(value instanceof Double){
                        Integer intValue = ((Double)value).intValue();
                        return intValue;
                    }
                    if(value instanceof String){
                        Integer intValue = Integer.valueOf((String)value);
                        return intValue;
                    }
                    break;
                }
                case BYTE:
                case SHORT: {
                    if(value instanceof Integer){
                        Short shortValue = ((Integer)value).shortValue();
                        return shortValue;
                    }
                    if(value instanceof Double){
                        Short shortValue = ((Double)value).shortValue();
                        return shortValue;
                    }
                    if(value instanceof String){
                        Short shortValue = Short.valueOf((String)value);
                        return shortValue;
                    }
                    break;
                }
                case CHAR: {
                    if(value instanceof Integer){
                        Short shortValue = ((Integer)value).shortValue();
                        Byte byteValue = shortValue.byteValue();
                        return byteValue;
                    }
                    if(value instanceof Double){
                        Short shortValue = ((Double)value).shortValue();
                        Byte byteValue = shortValue.byteValue();
                        return byteValue;
                    }
                    if(value instanceof String){
                        Byte byteValue = Byte.valueOf((String)value);
                        return byteValue;
                    }
                    break;
                }
                case LLONG:
                case DWORD: {
                    if(value instanceof Integer){
                        Long longValue = ((Integer)value).longValue();
                        return longValue;
                    }
                    if(value instanceof Double){
                        Long longValue = ((Double)value).longValue();
                        return longValue;
                    }
                    if(value instanceof String){
                        Long longValue = Long.valueOf((String)value);
                        return longValue;
                    }
                    break;
                }
                case FLOAT: {
                    if(value instanceof BigDecimal){
                        Float floatValue = ((BigDecimal)value).floatValue();
                        return floatValue;
                    }
                    if(value instanceof Double){
                        Float floatValue = ((Double)value).floatValue();
                        return floatValue;
                    }
                    if(value instanceof String){
                        Float floatValue = Float.valueOf((String)value);
                        return floatValue;
                    }
                    break;
                }
                case DOUBLE:{
                    if(value instanceof BigDecimal){
                        Double doubleValue = ((BigDecimal)value).doubleValue();
                        return doubleValue;
                    }
                    if(value instanceof String){
                        Double doubleValue = Double.valueOf((String)value);
                        return doubleValue;
                    }
                    break;
                }
                case STRING: {
                    break;
                }
                default:{
    
                    break;
                }
            }
        } catch (Exception e) {
            log.error("upData changeValueType exception: {}", e);
        }
        return value;
    }

    //返回是否改变
    private static Boolean dataChange(ThingDataTypeEnum dataType, Boolean isArray, Object oldValue, Object newValue){
        if(oldValue == null){
            if(newValue == null){
                return false;
            }else{
                return true;
            }
        }else{
            if(newValue == null){
                return true;
            }else{
                if(isArray){
                    List<Object> oldList = (List)oldValue;
                    List<Object> newList = (List)newValue;
                    Integer oldSize = oldList.size();
                    Integer newSize = newList.size();
                    if(!oldSize.equals(newSize)){
                        return true;
                    }
                    for(int i=0;i<oldSize;i++){
                        Boolean changed = dataChange(dataType, oldList.get(i), newList.get(i));
                        if(changed){
                            return true;
                        }
                    }
                    return false;
                }else{
                    return dataChange(dataType, oldValue, newValue);
                }
            }
        }
    }
    private static Boolean dataChange(ThingDataTypeEnum dataType, Object oldValue, Object newValue){
        try {
            Boolean changed = false;
            switch (dataType) {
                case BOOLEAN: {
                    if(oldValue == null){
                        if(newValue == null){
                            changed = false;
                        }else{
                            changed = true;
                        }
                    }else{
                        if(newValue == null){
                            changed = true;
                        }else{
                            Boolean oldV = (Boolean)oldValue;
                            Boolean newV = (Boolean)newValue;
                            if(oldV.equals(newV)){
                                changed = false;
                            }else{
                                changed = true;
                            }
                        }
                    }
                    break;
                }
                case LONG:
                case WORD:{
                    if(oldValue == null){
                        if(newValue == null){
                            changed = false;
                        }else{
                            changed = true;
                        }
                    }else{
                        if(newValue == null){
                            changed = true;
                        }else{
                            Integer oldV = (Integer)oldValue;
                            Integer newV = (Integer)newValue;
                            if(oldV.equals(newV)){
                                changed = false;
                            }else{
                                changed = true;
                            }
                        }
                    }
                    break;
                }
                case SHORT: 
                case BYTE:{
                    if(oldValue == null){
                        if(newValue == null){
                            changed = false;
                        }else{
                            changed = true;
                        }
                    }else{
                        if(newValue == null){
                            changed = true;
                        }else{
                            Short oldV = (Short)oldValue;
                            Short newV = (Short)newValue;
                            if(oldV.equals(newV)){
                                changed = false;
                            }else{
                                changed = true;
                            }
                        }
                    }
                    break;
                }
                case CHAR: {
                    if(oldValue == null){
                        if(newValue == null){
                            changed = false;
                        }else{
                            changed = true;
                        }
                    }else{
                        if(newValue == null){
                            changed = true;
                        }else{
                            Byte oldV = (Byte)oldValue;
                            Byte newV = (Byte)newValue;
                            if(oldV.equals(newV)){
                                changed = false;
                            }else{
                                changed = true;
                            }
                        }
                    }
                    break;
                }
                case LLONG:
                case DWORD: {
                    if(oldValue == null){
                        if(newValue == null){
                            changed = false;
                        }else{
                            changed = true;
                        }
                    }else{
                        if(newValue == null){
                            changed = true;
                        }else{
                            Long oldV = (Long)oldValue;
                            Long newV = (Long)newValue;
                            if(oldV.equals(newV)){
                                changed = false;
                            }else{
                                changed = true;
                            }
                        }
                    }
                    break;
                }
                case DOUBLE:{
                    if(oldValue == null){
                        if(newValue == null){
                            changed = false;
                        }else{
                            changed = true;
                        }
                    }else{
                        if(newValue == null){
                            changed = true;
                        }else{
                            Double oldV = (Double)oldValue;
                            Double newV = (Double)newValue;
                            if(oldV.equals(newV)){
                                changed = false;
                            }else{
                                changed = true;
                            }
                        }
                    }
                    break;
                }
                case FLOAT: {
                    if(oldValue == null){
                        if(newValue == null){
                            changed = false;
                        }else{
                            changed = true;
                        }
                    }else{
                        if(newValue == null){
                            changed = true;
                        }else{
                            Float oldV = (Float)oldValue;
                            Float newV = (Float)newValue;
                            if(oldV.equals(newV)){
                                changed = false;
                            }else{
                                changed = true;
                            }
                        }
                    }
                    break;
                }
                case STRING: {
                    if(oldValue == null){
                        if(newValue == null){
                            changed = false;
                        }else{
                            changed = true;
                        }
                    }else{
                        if(newValue == null){
                            changed = true;
                        }else{
                            String oldV = (String)oldValue;
                            String newV = (String)newValue;
                            if(oldV.equals(newV)){
                                changed = false;
                            }else{
                                changed = true;
                            }
                        }
                    }
                    break;
                }
                default:{
    
                    break;
                }
            }
    
            return changed;
        } catch (Exception e) {
            log.error("upData dataChange exception: {}", e);
            return true;
        }
    }
    public Map<String,Object> processSubscription(SubscriptionFromEnum from, List<UpProp> properties, Long timestamp){
        long start = System.currentTimeMillis();
        log.debug("上报链路，写入孪生：start：{}，属性个数：{}",start,properties.size());
        log.debug("dataChange2订阅处理,deviceId:{},properties:{}",deviceId,properties);
        if (properties == null || properties.size() <= 0) {
            return new HashMap<>();
        }

        Set<String> propertyNameSet = new HashSet<>();
        Map<String, Object> map = new HashMap<>();
        List<UpProp> propertyList = new ArrayList<>();

        properties.stream().filter(t ->
            Optional.ofNullable(t.getValue()).isPresent()
            && Optional.ofNullable(t.getProperty()).isPresent()
        ).forEach(t -> {
            ThingDataTypeEnum dataType = ThingDataTypeEnum.typeOfName(t.getDataType());
            Boolean isArray = t.getIsArray();
            Object oldValue = changeValueType(dataType, isArray, actual.get(t.getProperty()));
            Object newValue = changeValueType(dataType, isArray, t.getValue());
            t.setValue(newValue);

            if(dataChange(dataType, isArray, oldValue, newValue)){
                propertyNameSet.add(t.getProperty());
                map.put(t.getProperty(), t.getValue());
                propertyList.add(t);
            }
        });

        //写入孪生
        setProperties(deviceDataResource, deviceId, map);
        long endTime = System.currentTimeMillis();
        log.debug("上报链路，写入孪生：end:{},耗时：{}ms,属性个数：{}",endTime,endTime - start,properties.size());
        log.debug("上报链路，订阅触发：start:{}",endTime);
        //触发noChange更新过期时间
        {
            Map<String, Set<Subscription>> registry2Map = deviceDataResource.getNoChangeSubscriptionRegistry2Map(deviceId, propertyNameSet);
            propertyList.forEach((upProp) -> {
                String k = upProp.getProperty();
                Object v = upProp.getValue();
                if (registry2Map.containsKey(k)) {
                    registry2Map.get(k).forEach(subscription -> {
                        NoChangeInfo noChangeInfo = new NoChangeInfo(deviceId, k);
                        String key = noChangeInfo.toKey();
                        deviceDataResource.updateNoChangeExpireTime(key, subscription.getNoChangeSeconds());
                    });
                }
            });
        }


        //触发dataChange订阅
        {
            Map<String, Set<Subscription>> registry2Map = deviceDataResource.getSubscriptionRegistry2Map(deviceId, propertyNameSet);
            Map<Long, Subscription> subscriptions = new HashMap<>();
            propertyList.forEach((upProp) -> {
                String k = upProp.getProperty();
                Object v = upProp.getValue();
                if (registry2Map.containsKey(k)) {
                    registry2Map.get(k).forEach(subscription -> {
                        if (!subscriptions.containsKey(subscription.getId())) {
                            subscriptions.put(subscription.getId(),subscription);
                            EventData event = EventData.builder()
                                    .eventName("DataChange")
                                    .eventTime(timestamp)
                                    .eventData(new ArrayList<>())
                                    .build();
                            subscription.setEvent(event);
                        }else {
                            subscription = subscriptions.get(subscription.getId());
                        }
                        UpPropBuilder builder = UpProp.builder()
                                                .property(k)
                                                .value(v)
                                                .preValue(actual.get(k));
                        Long traceId = upProp.getTraceId();
                        if(traceId != null){
                            builder.traceId(traceId);
                            log.info("trace: {}, labelId:{}, property:{}, value: {}", traceId, upProp.getLabelId(), upProp.getProperty(), upProp.getValue());
                        }
                        UpProp property = builder.build();
                        subscription.getEvent().getEventData().add(property);
                    });
                }
            });
            actual.putAll(map);
            subscriptions.values().forEach(subscription -> {
                //如果是订阅，为了保持时间顺序，设备中的同一个订阅中，如果时间戳早于当前已经触发的订阅，则不触发
                if(SubscriptionOutTypeConstant.CALLBACK.equals(subscription.getOutType())){
                    boolean isLast = SubscriptionOrderCache.push2(deviceId, subscription.getId(), timestamp);
                    if(isLast){
                        subscription.subscriptionPostProcess(from, deviceId,actual,timestamp,SubscriptionEventTypeEnum.DATA_CHANGE);
                    }else{
                        log.debug("时间倒序 deviceId:{},subscriptionId:{} timestamp:{}", deviceId, subscription.getId(), timestamp);
                    }
                }else{
                    subscription.subscriptionPostProcess(from, deviceId,actual,timestamp,SubscriptionEventTypeEnum.DATA_CHANGE);
                }


            });
        }

        //触发事件，fault事件和trigger事件，及相关订阅
        processEvent(map,timestamp);
        long end = System.currentTimeMillis();
        log.debug("上报链路，订阅触发：end:{},耗时：{}ms",end,end - endTime);
        return map;
    }

    private void processEvent(Map<String, Object> map, Long timestamp) {
        ITaskService taskServiceImpl = ApplicationContextUtil.getBean("taskServiceImpl", TaskServiceImpl.class);
        ComputeTaskEntity task = taskServiceImpl.getComputeTaskByDeviceId(deviceId);
        if (!ObjectUtils.isEmpty(task)) {
            taskServiceImpl.executeTask(task,map,actual,timestamp);
        }

    }

    @Override
    public String toString() {
        return "DeviceTwin{" +
                "deviceId=" + deviceId +
                ", deviceDataResource=" + deviceDataResource +
                '}';
    }

    public Map<String, Object> getChangeTime() {
        return deviceDataResource.getChangeTimeById(deviceId);
    }

    public void initData() {
        actual = deviceDataResource.getById(deviceId);
    }
}
