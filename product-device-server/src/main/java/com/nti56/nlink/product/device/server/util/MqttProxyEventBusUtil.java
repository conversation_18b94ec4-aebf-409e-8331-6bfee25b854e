package com.nti56.nlink.product.device.server.util;

import com.alibaba.fastjson.JSONObject;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.MqttTopicEnum;
import com.nti56.nlink.product.device.server.verticle.MqttEdgeGatewayProxyVerticle;
import io.vertx.core.Vertx;
import io.vertx.core.eventbus.EventBus;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 类说明: MQTT代理EventBus工具类
 * 
 * 提供通过EventBus调用MQTT代理的方法
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-01-01 00:00:00
 * @since JDK 1.8
 */
@Component
@Slf4j
public class MqttProxyEventBusUtil {

    @Autowired
    private Vertx vertx;

    /**
     * 发送未分配网关请求
     * 
     * @param topicType MQTT主题类型
     * @param ioType 操作类型 (send/request)
     * @param imeiOrHost IMEI或主机地址
     * @param adminPort 管理端口
     * @param api API路径
     * @param body 请求体
     * @return 异步结果
     */
    public CompletableFuture<String> sendNotAssignRequest(MqttTopicEnum topicType, String ioType, 
                                                         String imeiOrHost, String adminPort, 
                                                         String api, String body) {
        CompletableFuture<String> future = new CompletableFuture<>();
        
        EventBus eventBus = vertx.eventBus();
        JsonObject requestBody = new JsonObject()
                .put("topicType", topicType.getTopicType())
                .put("ioType", ioType)
                .put("imeiOrHost", imeiOrHost)
                .put("adminPort", adminPort)
                .put("api", api)
                .put("body", body);

        if ("send".equals(ioType)) {
            // 发送消息，不需要回复
            eventBus.send(MqttEdgeGatewayProxyVerticle.EVENT_BUS_NOT_ASSIGN_TOPIC, requestBody);
            future.complete(JSONObject.toJSONString(Result.ok()));
        } else {
            // 请求消息，需要回复
            eventBus.request(MqttEdgeGatewayProxyVerticle.EVENT_BUS_NOT_ASSIGN_TOPIC, requestBody, 
                    reply -> {
                        if (reply.succeeded()) {
                            future.complete(reply.result().body().toString());
                        } else {
                            log.error("EventBus request failed", reply.cause());
                            future.complete(JSONObject.toJSONString(Result.error("请求失败: " + reply.cause().getMessage())));
                        }
                    });
        }
        
        return future;
    }

    /**
     * 发送已分配网关请求
     * 
     * @param topicType MQTT主题类型
     * @param ioType 操作类型 (send/request)
     * @param tenantId 租户ID
     * @param edgeGatewayId 边缘网关ID
     * @param api API路径
     * @param body 请求体
     * @return 异步结果
     */
    public CompletableFuture<String> sendAssignRequest(MqttTopicEnum topicType, String ioType, 
                                                      String tenantId, String edgeGatewayId, 
                                                      String api, String body) {
        CompletableFuture<String> future = new CompletableFuture<>();
        
        EventBus eventBus = vertx.eventBus();
        JsonObject requestBody = new JsonObject()
                .put("topicType", topicType.getTopicType())
                .put("ioType", ioType)
                .put("tenantId", tenantId)
                .put("edgeGatewayId", edgeGatewayId)
                .put("api", api)
                .put("body", body);

        if ("send".equals(ioType)) {
            // 发送消息，不需要回复
            eventBus.send(MqttEdgeGatewayProxyVerticle.EVENT_BUS_ASSIGN_TOPIC, requestBody);
            future.complete(JSONObject.toJSONString(Result.ok()));
        } else {
            // 请求消息，需要回复
            eventBus.request(MqttEdgeGatewayProxyVerticle.EVENT_BUS_ASSIGN_TOPIC, requestBody, 
                    reply -> {
                        if (reply.succeeded()) {
                            future.complete(reply.result().body().toString());
                        } else {
                            log.error("EventBus request failed", reply.cause());
                            future.complete(JSONObject.toJSONString(Result.error("请求失败: " + reply.cause().getMessage())));
                        }
                    });
        }
        
        return future;
    }

    /**
     * 同步发送未分配网关请求
     * 
     * @param topicType MQTT主题类型
     * @param ioType 操作类型 (send/request)
     * @param imeiOrHost IMEI或主机地址
     * @param adminPort 管理端口
     * @param api API路径
     * @param body 请求体
     * @param timeout 超时时间（秒）
     * @return 响应结果
     */
    public String sendNotAssignRequestSync(MqttTopicEnum topicType, String ioType, 
                                          String imeiOrHost, String adminPort, 
                                          String api, String body, long timeout) {
        try {
            return sendNotAssignRequest(topicType, ioType, imeiOrHost, adminPort, api, body)
                    .get(timeout, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("Send not assign request failed", e);
            return JSONObject.toJSONString(Result.error("请求超时或失败: " + e.getMessage()));
        }
    }

    /**
     * 同步发送已分配网关请求
     * 
     * @param topicType MQTT主题类型
     * @param ioType 操作类型 (send/request)
     * @param tenantId 租户ID
     * @param edgeGatewayId 边缘网关ID
     * @param api API路径
     * @param body 请求体
     * @param timeout 超时时间（秒）
     * @return 响应结果
     */
    public String sendAssignRequestSync(MqttTopicEnum topicType, String ioType, 
                                       String tenantId, String edgeGatewayId, 
                                       String api, String body, long timeout) {
        try {
            return sendAssignRequest(topicType, ioType, tenantId, edgeGatewayId, api, body)
                    .get(timeout, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("Send assign request failed", e);
            return JSONObject.toJSONString(Result.error("请求超时或失败: " + e.getMessage()));
        }
    }
} 