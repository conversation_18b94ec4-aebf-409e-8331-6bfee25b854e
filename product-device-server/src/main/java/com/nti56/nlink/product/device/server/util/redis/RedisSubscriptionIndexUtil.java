package com.nti56.nlink.product.device.server.util.redis;

import cn.hutool.core.collection.CollectionUtil;
import com.nti56.nlink.product.device.server.constant.RedisConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 类说明: Redis订阅索引工具类，用于优化订阅注册表键的查询性能
 * 替代原先的scan操作，提高查询效率
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024-01-01
 * @since JDK 1.8
 */
@Slf4j
@Component
public class RedisSubscriptionIndexUtil {

    /**
     * 获取设备的所有订阅注册表键
     * 支持旧数据兼容：当索引键不存在时，回退到scan操作并重建索引
     * 
     * @param redisTemplate Redis模板
     * @param deviceId 设备ID
     * @return 订阅注册表键集合
     */
    public static Set<String> getDeviceSubscriptionKeys(RedisTemplate<String, Object> redisTemplate, Long deviceId) {
        String subscriptionIndexKey = String.format(RedisConstant.SUBSCRIPTION_INDEX, deviceId);
        Set<Object> subscriptionKeys = redisTemplate.opsForSet().members(subscriptionIndexKey);
        
        // 如果索引键存在且不为空，直接返回
        if (CollectionUtil.isNotEmpty(subscriptionKeys)) {
            return subscriptionKeys.stream()
                    .map(Object::toString)
                    .collect(Collectors.toSet());
        }
        
        // 兼容旧数据：索引键不存在时，回退到scan操作并重建索引
        log.info("订阅索引键不存在，设备ID: {}，回退到scan操作并重建索引", deviceId);
        return getDeviceSubscriptionKeysWithFallback(redisTemplate, deviceId);
    }

    /**
     * 兼容旧数据的获取方法：使用scan操作并重建索引
     * 
     * @param redisTemplate Redis模板
     * @param deviceId 设备ID
     * @return 订阅注册表键集合
     */
    private static Set<String> getDeviceSubscriptionKeysWithFallback(RedisTemplate<String, Object> redisTemplate, Long deviceId) {
        RedisUtil redisUtil = new RedisUtil(redisTemplate, null);
        
        // 使用scan查找旧的订阅注册表键
        Long scanBegin = System.currentTimeMillis();
        Set<String> keys = redisUtil.scan(String.format(RedisConstant.SUBSCRIPTION_REGISTRY, deviceId, "*"));
        Long scanEnd = System.currentTimeMillis();
        log.info("兼容模式scan操作耗时: {} ms, 设备ID: {}, 找到键数量: {}", 
                scanEnd - scanBegin, deviceId, keys.size());
        
        // 如果找到了键，重建索引以备下次使用
        if (CollectionUtil.isNotEmpty(keys)) {
            log.info("为设备 {} 重建订阅索引，键数量: {}", deviceId, keys.size());
            addSubscriptionKeysToIndex(redisTemplate, deviceId, keys);
        }
        
        return keys;
    }

    /**
     * 添加订阅注册表键到索引
     * 
     * @param redisTemplate Redis模板
     * @param deviceId 设备ID
     * @param subscriptionKey 订阅注册表键
     */
    public static void addSubscriptionKeyToIndex(RedisTemplate<String, Object> redisTemplate, Long deviceId, String subscriptionKey) {
        String subscriptionIndexKey = String.format(RedisConstant.SUBSCRIPTION_INDEX, deviceId);
        redisTemplate.opsForSet().add(subscriptionIndexKey, subscriptionKey);
    }

    /**
     * 批量添加订阅注册表键到索引
     * 
     * @param redisTemplate Redis模板
     * @param deviceId 设备ID
     * @param subscriptionKeys 订阅注册表键集合
     */
    public static void addSubscriptionKeysToIndex(RedisTemplate<String, Object> redisTemplate, Long deviceId, Set<String> subscriptionKeys) {
        if (CollectionUtil.isNotEmpty(subscriptionKeys)) {
            String subscriptionIndexKey = String.format(RedisConstant.SUBSCRIPTION_INDEX, deviceId);
            redisTemplate.opsForSet().add(subscriptionIndexKey, subscriptionKeys.toArray());
        }
    }

    /**
     * 从索引中移除订阅注册表键
     * 
     * @param redisTemplate Redis模板
     * @param deviceId 设备ID
     * @param subscriptionKey 订阅注册表键
     */
    public static void removeSubscriptionKeyFromIndex(RedisTemplate<String, Object> redisTemplate, Long deviceId, String subscriptionKey) {
        String subscriptionIndexKey = String.format(RedisConstant.SUBSCRIPTION_INDEX, deviceId);
        redisTemplate.opsForSet().remove(subscriptionIndexKey, subscriptionKey);
    }

    /**
     * 清空设备的订阅索引
     * 
     * @param redisTemplate Redis模板
     * @param deviceId 设备ID
     */
    public static void clearDeviceSubscriptionIndex(RedisTemplate<String, Object> redisTemplate, Long deviceId) {
        String subscriptionIndexKey = String.format(RedisConstant.SUBSCRIPTION_INDEX, deviceId);
        redisTemplate.delete(subscriptionIndexKey);
    }

    /**
     * 获取设备的订阅索引键
     * 
     * @param deviceId 设备ID
     * @return 订阅索引键
     */
    public static String getSubscriptionIndexKey(Long deviceId) {
        return String.format(RedisConstant.SUBSCRIPTION_INDEX, deviceId);
    }
} 