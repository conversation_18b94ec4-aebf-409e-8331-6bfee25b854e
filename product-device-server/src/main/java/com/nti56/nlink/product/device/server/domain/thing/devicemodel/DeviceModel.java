package com.nti56.nlink.product.device.server.domain.thing.devicemodel;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.util.BeanUtilsIntensifier;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
import com.nti56.nlink.product.device.client.model.dto.json.modelfield.ServiceElm;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ModelDpo;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ModelTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.*;
import com.nti56.nlink.product.device.server.domain.thing.thingmodel.ThingModel;
import com.nti56.nlink.product.device.server.domain.thing.thingmodel.ThingModelInherit;
import com.nti56.nlink.product.device.server.entity.*;
import com.nti56.nlink.product.device.server.model.deviceModel.DeviceModelBo;
import com.nti56.nlink.product.device.server.model.deviceModel.PropertyBo;
import lombok.Getter;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 类说明: 设备模型领域对象
 * 
 * 可以继承物模型，但不能被继承
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-04-28 09:06:02
 * @since JDK 1.8
 */
@Getter
public class DeviceModel {
    
    private Long deviceId;

    private String name;

    private String descript;

    //继承部分
    private ThingModelInherit inherit;

    private List<DeviceModelInheritEntity> inheritRows;

    //自己部分
    private List<Property> selfProperties;
    private List<Event> selfEvents;
    private List<Service> selfServices;
    private List<Subscription> selfSubscriptions;

    private List<DeviceServiceEntity> selfServiceRows;

    //继承部分 merge 自己部分
    private List<Property> properties;
    private List<Event> events;
    private List<Service> services;
    private List<Subscription> subscriptions;

    public static ThingModel getBaseModel(){
        ModelDpo baseModel = JSONUtil.toBean(ThingModel.deviceBaseModelStr, ModelDpo.class);
        ThingModel thingModel =  ThingModel.buildByDpo(baseModel);
        return thingModel;
    }

    public static Result<DeviceModel> checkInfo(
        DeviceEntity entity, 
        CommonFetcher commonFetcher
    ){
        if(entity == null){
            return Result.error("设备模型不存在");
        }

        DeviceModel deviceModel = new DeviceModel();
        
        if(entity.getId() == null){
            return Result.error("设备id不能为空");
        }
        deviceModel.deviceId = entity.getId();
        deviceModel.name = entity.getName();
        deviceModel.descript = entity.getDescript();
        
        //继承物模型
        List<DeviceModelInheritEntity> inheritEntityList = commonFetcher.list("device_id", entity.getId(), DeviceModelInheritEntity.class);
        List<Long> inheritThingModelIdList = inheritEntityList.stream()
            .map(DeviceModelInheritEntity::getInheritThingModelId)
            .collect(Collectors.toList());
        Result<ThingModelInherit> inheritResult = ThingModelInherit.checkInfo(
            inheritThingModelIdList,
            commonFetcher,
            ThingModel.THING_MODEL_ID_PATH_SPLITER
        );
        if(!inheritResult.getSignal()){
            return Result.error(inheritResult.getMessage() + ", deviceId" + entity.getId());
        }
        deviceModel.inheritRows = inheritEntityList;

        deviceModel.inherit = inheritResult.getResult();

        ModelField model = entity.getModel();

        //继承的属性
        List<Property> inheritProperties = deviceModel.inherit.getProperties();

        //自己的属性
        Result<List<Property>> selfPropertiesResult = Property.batchCheckInfo(
            null, null,
            entity.getId(), entity.getName(), 
            model
        );
        if(!selfPropertiesResult.getSignal()){
            return Result.error(selfPropertiesResult.getMessage() + ", deviceId" + entity.getId());
        }
        List<Property> selfProperties = selfPropertiesResult.getResult();

        //检查属性重复
        Result<List<Property>> propertiesResult = Property.checkRepeat(
            inheritProperties, selfProperties
        );
        if(!propertiesResult.getSignal()){
            return Result.error(propertiesResult.getMessage() + ", deviceId" + entity.getId());
        }
        List<Property> properties = propertiesResult.getResult();
        deviceModel.properties = properties;
        deviceModel.selfProperties = selfProperties;

        List<Property> allProperty = new ArrayList<>();
        allProperty.addAll(deviceModel.properties);
        allProperty.addAll(getBaseModel().getProperties());


        //继承的事件
        List<Event> inheritEvents = deviceModel.inherit.getEvents();

        //自己的事件
        Result<List<Event>> selfEventsResult = Event.batchCheckInfo(null,null,
            model, allProperty
        );
        if(!selfEventsResult.getSignal()){
            return Result.error(selfEventsResult.getMessage() + ", deviceId" + entity.getId());
        }
        List<Event> selfEvents = selfEventsResult.getResult();

        //检查事件重复
        Result<List<Event>> eventsResult = Event.checkRepeat(
            inheritEvents, selfEvents
        );
        if(!eventsResult.getSignal()){
            return Result.error(eventsResult.getMessage() + ", deviceId" + entity.getId());
        }
        deviceModel.events = eventsResult.getResult();
        deviceModel.selfEvents = selfEvents;

        //继承的服务
        List<Service> inheritServices = deviceModel.inherit.getServices();

        //自己的服务
        List<DeviceServiceEntity> deviceServiceEntities = commonFetcher.list("device_id", entity.getId(), DeviceServiceEntity.class);
        if(CollectionUtil.isNotEmpty(deviceServiceEntities)){
            deviceServiceEntities = deviceServiceEntities.stream().sorted(Comparator.comparing(DeviceServiceEntity::getCreateTime).reversed()).collect(Collectors.toList());
        }
        List<ServiceEntityBase> serviceEntities = deviceServiceEntities.stream().map(t -> {
            ServiceEntityBase target = new ServiceEntityBase();
            BeanUtils.copyProperties(t, target);
            return target;
        }).collect(Collectors.toList());
        Result<List<Service>> selfServicesResult = Service.batchCheckInfo(null,null, serviceEntities);
        if(!selfServicesResult.getSignal()){
            return Result.error(selfServicesResult.getMessage() + ", deviceId" + entity.getId());
        }
        List<Service> selfServices = selfServicesResult.getResult();
        deviceModel.selfServiceRows = deviceServiceEntities;
        
        //检查继承的物模型中是否服务重名
        Result<List<Service>> servicesNameCheckResult=Service.checkRepeat(inheritServices);
        if(!servicesNameCheckResult.getSignal()){
            return Result.error(servicesNameCheckResult.getMessage() + ", deviceId:" + entity.getId());
        }

        //检查自己的物模型中是否服务重名
        Result<List<Service>> selfServicesNameCheckResult=Service.checkRepeat(selfServices);
        if(!selfServicesNameCheckResult.getSignal()){
            return Result.error(selfServicesNameCheckResult.getMessage() + ", deviceId" + entity.getId());
        }

        //检查服务覆盖
        Result<List<Service>> servicesResult = Service.checkOverride(
            inheritServices, selfServices
        );
        if(!servicesResult.getSignal()){
            return Result.error(servicesResult.getMessage() + ", deviceId" + entity.getId());
        }
        List<Service> services = servicesResult.getResult();
        deviceModel.services = services;
        deviceModel.selfServices = selfServices;

        //继承的订阅
        List<Subscription> inheritSubscriptions = deviceModel.inherit.getSubscriptions();

        //自己的订阅
        List<SubscriptionEntity> SubscriptionEntities = commonFetcher.preloader("directly_model_id", entity.getId(), SubscriptionEntity.class)
                .filter(SubscriptionEntity::getModelType, ModelTypeEnum.DEVICE_MODEL.getValue());
        if(CollectionUtil.isNotEmpty(SubscriptionEntities)){
            SubscriptionEntities = SubscriptionEntities.stream().sorted(Comparator.comparing(SubscriptionEntity::getCreateTime).reversed()).collect(Collectors.toList());
        }
        Result<List<Subscription>> selfSubscriptionsResult = Subscription.batchCheckInfo(null,null,SubscriptionEntities);
        if(!selfSubscriptionsResult.getSignal()){
            return Result.error(selfSubscriptionsResult.getMessage() + ", deviceId:" + entity.getId());
        }
        List<Subscription> selfSubscriptions = selfSubscriptionsResult.getResult();

        //检查自己的物模型中是否订阅重名
        Result<List<Subscription>> subscriptionResult = Subscription.checkRepeat(inheritSubscriptions,selfSubscriptions);
        if(!subscriptionResult.getSignal()){
            return Result.error(subscriptionResult.getMessage() + ", deviceId:" + entity.getId());
        }

        deviceModel.subscriptions = subscriptionResult.getResult();
        deviceModel.selfSubscriptions = selfSubscriptions;

        return Result.ok(deviceModel);
    }

    public DeviceModelBo toDeviceModelBo() {
        DeviceModelBo bo = new DeviceModelBo();
        bo.setProperties(new ArrayList<>());
        bo.setEvents(new ArrayList<>());
        bo.setServices(new ArrayList<>());
        Optional.ofNullable(this.properties).orElse(new ArrayList<>()).forEach(property -> {
            PropertyBo propertyBo = BeanUtilsIntensifier.copyBean(property.getRaw(), PropertyBo.class);
            
            propertyBo.setDirectlyModelId(property.getDirectlyModelId());
            propertyBo.setModelType(property.getModelType().getValue());
            
            bo.getProperties().add(propertyBo);
        });
        Optional.ofNullable(this.events).orElse(new ArrayList<>()).forEach(event -> {
            bo.getEvents().add(event.getRaw());
        });
        Optional.ofNullable(this.services).orElse(new ArrayList<>()).forEach(service -> {
            ServiceElm serviceElm = BeanUtilsIntensifier.copyBean(service, ServiceElm.class);
            serviceElm.setName(service.getServiceName());
            bo.getServices().add(serviceElm);
        });
        return bo;
    }

    public ModelDpo toSelfDpo(){
        
        ModelDpo dpo = new ModelDpo();
        dpo.setId(deviceId);
        dpo.setName(name);
        dpo.setDescript(descript);
        
        if(selfProperties != null){
            dpo.setProperties(selfProperties.stream()
                .map(Property::toDpo)
                .collect(Collectors.toList())
            );
        }
        if(selfEvents != null){
            dpo.setEvents(selfEvents.stream()
                .map(Event::toDpo)
                .collect(Collectors.toList())
            );
        }
        if(selfServices != null){
            dpo.setServices(selfServices.stream()
                .map(Service::toDpo)
                .collect(Collectors.toList())
            );
        }
        if(selfSubscriptions != null){
            dpo.setSubscriptions(selfSubscriptions.stream()
                    .map(Subscription::toDpo)
                    .collect(Collectors.toList())
            );
        }
        return dpo;
    }
    
    public ModelDpo toDpo(){
        ModelDpo dpo = new ModelDpo();
        dpo.setId(deviceId);
        dpo.setName(name);
        dpo.setDescript(descript);
        
        if(properties != null){
            dpo.setProperties(properties.stream()
                .map(Property::toDpo)
                .collect(Collectors.toList())
            );
        }
        if(events != null){
            dpo.setEvents(events.stream()
                .map(Event::toDpo)
                .collect(Collectors.toList())
            );
        }
        if(services != null){
            dpo.setServices(services.stream()
                .map(Service::toDpo)
                .collect(Collectors.toList())
            );
        }
        if(subscriptions != null){
            dpo.setSubscriptions(subscriptions.stream()
                    .map(Subscription::toDpo)
                    .collect(Collectors.toList())
            );
        }
        return dpo;
    }

    public Property getProperty(String propertyName) {
        Optional<Property> findAny = properties.stream()
                    .filter(t -> {
                        return t.getName().equals(propertyName);
                    })
                    .findAny();
        if(findAny.isPresent()){
            return findAny.get();
        }
        return null;
    }


    public static Result<DeviceModel> checkInfoNew(
            DeviceEntity entity,
            Map<Long,ThingModelEntity> thingModelEntityMap,//根据继承modelId获取到的map
            Map<Long,List<ThingModelInheritEntity>> thingModelInheritEntityMap, //根据继承modelId获取到的map
            Map<Long,List<ThingServiceEntity>> thingServiceEntityMap,   //根据继承modelId获取到的map
            Map<Long,List<SubscriptionEntity>> subscriptionEntityMap, //根据继承deviceId,modelId获取到的map
            Map<Long,List<DeviceModelInheritEntity>> deviceModelinheritEntityListMap,  // 根据deviceId
            Map<Long,List<DeviceServiceEntity>> deviceServiceEntityMap  //根据deviceId


    ){
        if(entity == null){
            return Result.error("设备模型不存在");
        }

        DeviceModel deviceModel = new DeviceModel();

        if(entity.getId() == null){
            return Result.error("设备id不能为空");
        }
        deviceModel.deviceId = entity.getId();
        deviceModel.name = entity.getName();
        deviceModel.descript = entity.getDescript();

        //继承物模型
        List<DeviceModelInheritEntity> inheritEntityList = deviceModelinheritEntityListMap.get(entity.getId());
        if(CollectionUtil.isEmpty(inheritEntityList)){
            inheritEntityList = new ArrayList<>();
        }
        List<Long> inheritThingModelIdList = inheritEntityList.stream()
                .map(DeviceModelInheritEntity::getInheritThingModelId)
                .collect(Collectors.toList());
        Result<ThingModelInherit> inheritResult = ThingModelInherit.checkInfoNew(
                inheritThingModelIdList,
                ThingModel.THING_MODEL_ID_PATH_SPLITER,
                thingModelEntityMap,
                thingModelInheritEntityMap,
                thingServiceEntityMap,
                subscriptionEntityMap
        );
        if(!inheritResult.getSignal()){
            return Result.error(inheritResult.getMessage() + ", deviceId" + entity.getId());
        }
        deviceModel.inheritRows = inheritEntityList;

        deviceModel.inherit = inheritResult.getResult();

        ModelField model = entity.getModel();

        //继承的属性
        List<Property> inheritProperties = deviceModel.inherit.getProperties();

        //自己的属性
        Result<List<Property>> selfPropertiesResult = Property.batchCheckInfo(
                null, null,
                entity.getId(), entity.getName(),
                model
        );
        if(!selfPropertiesResult.getSignal()){
            return Result.error(selfPropertiesResult.getMessage() + ", deviceId" + entity.getId());
        }
        List<Property> selfProperties = selfPropertiesResult.getResult();

        //检查属性重复
        Result<List<Property>> propertiesResult = Property.checkRepeat(
                inheritProperties, selfProperties
        );
        if(!propertiesResult.getSignal()){
            return Result.error(propertiesResult.getMessage() + ", deviceId" + entity.getId());
        }
        List<Property> properties = propertiesResult.getResult();
        deviceModel.properties = properties;
        deviceModel.selfProperties = selfProperties;

        List<Property> allProperty = new ArrayList<>();
        allProperty.addAll(deviceModel.properties);
        allProperty.addAll(getBaseModel().getProperties());


        //继承的事件
        List<Event> inheritEvents = deviceModel.inherit.getEvents();

        //自己的事件
        Result<List<Event>> selfEventsResult = Event.batchCheckInfo(null,null,
                model, allProperty
        );
        if(!selfEventsResult.getSignal()){
            return Result.error(selfEventsResult.getMessage() + ", deviceId" + entity.getId());
        }
        List<Event> selfEvents = selfEventsResult.getResult();

        //检查事件重复
        Result<List<Event>> eventsResult = Event.checkRepeat(
                inheritEvents, selfEvents
        );
        if(!eventsResult.getSignal()){
            return Result.error(eventsResult.getMessage() + ", deviceId" + entity.getId());
        }
        deviceModel.events = eventsResult.getResult();
        deviceModel.selfEvents = selfEvents;

        //继承的服务
        List<Service> inheritServices = deviceModel.inherit.getServices();

        //自己的服务
        List<DeviceServiceEntity> deviceServiceEntities = deviceServiceEntityMap.get(entity.getId());
        if(CollectionUtil.isEmpty(deviceServiceEntities)){
            deviceServiceEntities = new ArrayList<>();
        }
        deviceServiceEntities = deviceServiceEntities.stream().sorted(Comparator.comparing(DeviceServiceEntity::getCreateTime).reversed()).collect(Collectors.toList());

        List<ServiceEntityBase> serviceEntities = deviceServiceEntities.stream().map(t -> {
            ServiceEntityBase target = new ServiceEntityBase();
            BeanUtils.copyProperties(t, target);
            return target;
        }).collect(Collectors.toList());
        Result<List<Service>> selfServicesResult = Service.batchCheckInfo(null,null, serviceEntities);
        if(!selfServicesResult.getSignal()){
            return Result.error(selfServicesResult.getMessage() + ", deviceId" + entity.getId());
        }
        List<Service> selfServices = selfServicesResult.getResult();
        deviceModel.selfServiceRows = deviceServiceEntities;

        //检查继承的物模型中是否服务重名
        Result<List<Service>> servicesNameCheckResult=Service.checkRepeat(inheritServices);
        if(!servicesNameCheckResult.getSignal()){
            return Result.error(servicesNameCheckResult.getMessage() + ", deviceId:" + entity.getId());
        }

        //检查自己的物模型中是否服务重名
        Result<List<Service>> selfServicesNameCheckResult=Service.checkRepeat(selfServices);
        if(!selfServicesNameCheckResult.getSignal()){
            return Result.error(selfServicesNameCheckResult.getMessage() + ", deviceId" + entity.getId());
        }

        //检查服务覆盖
        Result<List<Service>> servicesResult = Service.checkOverride(
                inheritServices, selfServices
        );
        if(!servicesResult.getSignal()){
            return Result.error(servicesResult.getMessage() + ", deviceId" + entity.getId());
        }
        List<Service> services = servicesResult.getResult();
        deviceModel.services = services;
        deviceModel.selfServices = selfServices;

        //继承的订阅
        List<Subscription> inheritSubscriptions = deviceModel.inherit.getSubscriptions();

        //自己的订阅
        List<SubscriptionEntity> subscriptionEntities = subscriptionEntityMap.get(entity.getId());

        if(CollectionUtil.isNotEmpty(subscriptionEntities)){
            subscriptionEntities = subscriptionEntities.stream().filter(f->ModelTypeEnum.DEVICE_MODEL.getValue().toString().equals(String.valueOf(f.getModelType()))).collect(Collectors.toList());
        }
        if(CollectionUtil.isNotEmpty(subscriptionEntities)){
            subscriptionEntities = subscriptionEntities.stream().sorted(Comparator.comparing(SubscriptionEntity::getCreateTime).reversed()).collect(Collectors.toList());
        }
        Result<List<Subscription>> selfSubscriptionsResult = Subscription.batchCheckInfo(null,null,subscriptionEntities);
        if(!selfSubscriptionsResult.getSignal()){
            return Result.error(selfSubscriptionsResult.getMessage() + ", deviceId:" + entity.getId());
        }
        List<Subscription> selfSubscriptions = selfSubscriptionsResult.getResult();

        //检查自己的物模型中是否订阅重名
        Result<List<Subscription>> subscriptionResult = Subscription.checkRepeat(inheritSubscriptions,selfSubscriptions);
        if(!subscriptionResult.getSignal()){
            return Result.error(subscriptionResult.getMessage() + ", deviceId:" + entity.getId());
        }

        deviceModel.subscriptions = subscriptionResult.getResult();
        deviceModel.selfSubscriptions = selfSubscriptions;

        return Result.ok(deviceModel);
    }
    
}
