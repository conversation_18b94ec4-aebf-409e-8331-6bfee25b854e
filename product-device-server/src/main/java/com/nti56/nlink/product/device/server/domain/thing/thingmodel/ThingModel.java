package com.nti56.nlink.product.device.server.domain.thing.thingmodel;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.nti56.nlink.common.fetcher.CommonFetcher;
import com.nti56.nlink.common.updater.CommonUpdater;
import com.nti56.nlink.common.util.Result;
import com.nti56.nlink.product.device.client.model.dto.json.ModelField;
import com.nti56.nlink.product.device.server.constant.Constant;
import com.nti56.nlink.product.device.server.constant.GlobalVariables;
import com.nti56.nlink.product.device.server.domain.thing.device.Device;
import com.nti56.nlink.product.device.server.domain.thing.devicemodel.DeviceModel;
import com.nti56.nlink.product.device.server.domain.thing.dpo.ModelDpo;
import com.nti56.nlink.product.device.server.domain.thing.enumerate.ModelTypeEnum;
import com.nti56.nlink.product.device.server.domain.thing.modelbase.*;
import com.nti56.nlink.product.device.server.entity.*;
import lombok.Data;
import lombok.Getter;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 类说明: 物模型领域对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-03-24 16:37:58
 * @since JDK 1.8
 */
@Getter
public class ThingModel {
    
    private Long id;

    private String name;

    private String descript;

    private ThingModelInherit inherit;

    private List<ThingModelInheritEntity> inheritRows;

    private List<Property> properties;

    private List<Event> events;

    private List<Service> services;

    private List<Subscription> subscriptions;

    private ThingModelEntity row;

    private Integer modelType;

    public static String deviceBaseModelStr = "{\"name\":\"设备基础模型\",\"descript\":\"设备基础模型\",\"properties\":[{\"name\":\"id\",\"descript\":\"设备资源ID\",\"dataType\":{\"type\":\"Long\",\"isArray\":false},\"reportType\":0,\"bindLabel\":false,\"readOnly\":true,\"persist\":false,\"required\":false},{\"name\":\"name\",\"descript\":\"设备名称\",\"dataType\":{\"type\":\"String\",\"isArray\":false},\"reportType\":0,\"bindLabel\":false,\"readOnly\":false,\"persist\":false,\"required\":false},{\"name\":\"description\",\"descript\":\"设备描述\",\"dataType\":{\"type\":\"String\",\"isArray\":false},\"reportType\":0,\"bindLabel\":false,\"readOnly\":false,\"persist\":false,\"required\":false},{\"name\":\"status\",\"descript\":\"设备状态\",\"dataType\":{\"type\":\"Short\",\"isArray\":false},\"reportType\":0,\"bindLabel\":false,\"readOnly\":false,\"persist\":false,\"required\":false}],\"events\":[],\"services\":[],\"subscriptions\":[],\"modelType\":5}";

    public static final String THING_MODEL_ID_PATH_SPLITER = "/";

    public static ThingModel buildByDpo(ModelDpo dpo) {
        ThingModel thingModel =  new ThingModel();
        thingModel.name = dpo.getName();
        thingModel.properties = new ArrayList<>();
        dpo.getProperties().forEach(propertyDpo -> {
            thingModel.properties.add(Property.buildByDpo(propertyDpo));
        });
        return thingModel;
    }

    //获取与模型相关的继承的模型id列表
    public List<Long> listAllThingModelId(){
        List<Long> list = new ArrayList<>();
        list.add(id);
        List<Long> inheritIds = inherit.listAllThingModelId();
        if(inheritIds != null && inheritIds.size() > 0){
            list.addAll(inheritIds);
        }
        return list;
    }

    public static Result<ThingModel> checkInfo(
        ThingModelEntity entity, 
        CommonFetcher commonFetcher
    ){
        return checkInfo(
            entity, 
            commonFetcher,
            THING_MODEL_ID_PATH_SPLITER
        );
    }

    public static ThingModel getBaseModel(){
        ModelDpo baseModel = JSONUtil.toBean(deviceBaseModelStr, ModelDpo.class);
        return buildByDpo(baseModel);
    }

    /**
     * 检查自己模型、继承模型
     */
    public static Result<ThingModel> checkInfo(
        ThingModelEntity entity, 
        CommonFetcher commonFetcher,
        String thingModelIdPath
    ){

        if(entity == null){
            return Result.error("物模型不存在");
        }

        ThingModel thingModel = new ThingModel();

        if(entity.getId() == null){
            return Result.error("物模型id不能为空");
        }
        thingModel.id = entity.getId();
        thingModel.name = entity.getName();
        thingModel.descript = entity.getDescript();
        thingModel.modelType = entity.getModelType();
        thingModel.row = entity;

        //继承
        List<ThingModelInheritEntity> inheritEntityList = commonFetcher.list(
            "thing_model_id", entity.getId(), ThingModelInheritEntity.class
        );
        List<Long> inheritThingModelIdList = inheritEntityList.stream()
            .map(ThingModelInheritEntity::getInheritThingModelId)
            .collect(Collectors.toList());
        Result<ThingModelInherit> inheritResult = ThingModelInherit.checkInfo(
            inheritThingModelIdList,
            commonFetcher,
            thingModelIdPath + entity.getId() + THING_MODEL_ID_PATH_SPLITER
        );
        if(!inheritResult.getSignal()){
            return Result.error(inheritResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        thingModel.inherit = inheritResult.getResult();
        thingModel.inheritRows = inheritEntityList;
        ModelField model = entity.getModel();

        //继承的属性
        List<Property> inheritProperties = thingModel.inherit.getProperties();

        //自己的属性
        Result<List<Property>> selfPropertiesResult = Property.batchCheckInfo(
            entity.getId(), entity.getName(), 
            null, null,
            model
        );
        if(!selfPropertiesResult.getSignal()){
            return Result.error(selfPropertiesResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        List<Property> selfProperties = selfPropertiesResult.getResult();

        //检查属性重复
        Result<List<Property>> propertiesResult = Property.checkRepeat(
            inheritProperties, selfProperties
        );
        if(!propertiesResult.getSignal()){
            return Result.error(propertiesResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        List<Property> properties = propertiesResult.getResult();
        thingModel.properties = properties;

        //继承的事件
        List<Event> inheritEvents = thingModel.inherit.getEvents();

        List<Property> allProperty = new ArrayList<>();
        allProperty.addAll(thingModel.properties);
        allProperty.addAll(getBaseModel().getProperties());

        //自己的事件
        Result<List<Event>> selfEventsResult = Event.batchCheckInfo(entity.getId(),entity.getName(),
            model, allProperty
        );
        if(!selfEventsResult.getSignal()){
            return Result.error(selfEventsResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        List<Event> selfEvents = selfEventsResult.getResult();

        //检查事件重复
        Result<List<Event>> eventsResult = Event.checkRepeat(
            inheritEvents, selfEvents
        );
        if(!eventsResult.getSignal()){
            return Result.error(eventsResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        thingModel.events = eventsResult.getResult();

        //继承的服务
        List<Service> inheritServices = thingModel.inherit.getServices();

        //自己的服务
        List<ThingServiceEntity> thingServiceEntities = commonFetcher.list(
            "thing_model_id", entity.getId(), ThingServiceEntity.class
        );
        if (ObjectUtil.equals(entity.getId(), Constant.DEFAULT_THING)){
            thingServiceEntities = GlobalVariables.getDefaultCommonTypeService();
        }
        if(CollectionUtil.isNotEmpty(thingServiceEntities)){
            thingServiceEntities = thingServiceEntities.stream().sorted(Comparator.comparing(ThingServiceEntity::getCreateTime).reversed()).collect(Collectors.toList());
        }
        List<ServiceEntityBase> serviceEntities = thingServiceEntities.stream().map(t -> {
            ServiceEntityBase target = new ServiceEntityBase();
            BeanUtils.copyProperties(t, target);
            return target;
        }).collect(Collectors.toList());
        Result<List<Service>> selfServicesResult = Service.batchCheckInfo(entity.getId(),entity.getName(),serviceEntities);
        if(!selfServicesResult.getSignal()){
            return Result.error(selfServicesResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        List<Service> selfServices = selfServicesResult.getResult();
        
        //检查自己的物模型中是否服务重名
        Result<List<Service>> selfServicesNameCheckResult=Service.checkRepeat(selfServices);
        if(!selfServicesNameCheckResult.getSignal()){
            return Result.error(selfServicesNameCheckResult.getMessage() + ", 模型ID:" + entity.getId());
        }
        //检查服务覆盖
        Result<List<Service>> servicesResult = Service.checkOverride(
            inheritServices, selfServices
        );
        if(!servicesResult.getSignal()){
            return Result.error(servicesResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        thingModel.services = servicesResult.getResult();

        //继承的订阅
        List<Subscription> inheritSubscriptions = thingModel.inherit.getSubscriptions();

        //自己的订阅
        List<SubscriptionEntity> SubscriptionEntities = commonFetcher.preloader("directly_model_id", entity.getId(), SubscriptionEntity.class)
                .filter(SubscriptionEntity::getModelType, ModelTypeEnum.THING_MODEL.getValue());
        if(CollectionUtil.isNotEmpty(SubscriptionEntities)){
            SubscriptionEntities = SubscriptionEntities.stream().sorted(Comparator.comparing(SubscriptionEntity::getCreateTime).reversed()).collect(Collectors.toList());
        }

        Result<List<Subscription>> selfSubscriptionsResult = Subscription.batchCheckInfo(entity.getId(),entity.getName(),SubscriptionEntities);
        if(!selfSubscriptionsResult.getSignal()){
            return Result.error(selfSubscriptionsResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        List<Subscription> selfSubscriptions = selfSubscriptionsResult.getResult();

        //检查自己的物模型中是否订阅重名
        Result<List<Subscription>> subscriptionResult = Subscription.checkRepeat(inheritSubscriptions,selfSubscriptions);
        if(!subscriptionResult.getSignal()){
            return Result.error(subscriptionResult.getMessage() + ", 模型ID:" + entity.getId());
        }

        thingModel.subscriptions = subscriptionResult.getResult();

        return Result.ok(thingModel);
    }

    public static Result<ThingModel> checkSubscription(ThingModelEntity entity,CommonFetcher commonFetcher){
        if(entity == null){
            return Result.error("物模型不存在");
        }

        ThingModel thingModel = new ThingModel();

        if(entity.getId() == null){
            return Result.error("物模型id不能为空");
        }
        thingModel.id = entity.getId();

        List<ThingModelInheritEntity> inheritEntityList = commonFetcher.list(
                "thing_model_id", entity.getId(), ThingModelInheritEntity.class
        );
        List<Long> inheritThingModelIdList = inheritEntityList.stream()
                .map(ThingModelInheritEntity::getInheritThingModelId)
                .collect(Collectors.toList());
        Result<ThingModelInherit> inheritResult = ThingModelInherit.checkInfo(
                inheritThingModelIdList,
                commonFetcher,
                THING_MODEL_ID_PATH_SPLITER + entity.getId() + THING_MODEL_ID_PATH_SPLITER
        );
        if(!inheritResult.getSignal()){
            return Result.error(inheritResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        thingModel.inherit = inheritResult.getResult();
        //继承的订阅
        List<Subscription> inheritSubscriptions = thingModel.inherit.getSubscriptions();

        //自己的订阅
        List<SubscriptionEntity> SubscriptionEntities = commonFetcher.preloader("directly_model_id", entity.getId(), SubscriptionEntity.class)
                .filter(SubscriptionEntity::getModelType, ModelTypeEnum.THING_MODEL.getValue());

        Result<List<Subscription>> selfSubscriptionsResult = Subscription.batchCheckInfo(entity.getId(),entity.getName(),SubscriptionEntities);
        if(!selfSubscriptionsResult.getSignal()){
            return Result.error(selfSubscriptionsResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        List<Subscription> selfSubscriptions = selfSubscriptionsResult.getResult();

        //检查自己的物模型中是否订阅重名
        Result<List<Subscription>> subscriptionResult = Subscription.checkRepeat(inheritSubscriptions,selfSubscriptions);
        if(!subscriptionResult.getSignal()){
            return Result.error(subscriptionResult.getMessage() + ", 模型ID:" + entity.getId());
        }
        return Result.ok(thingModel);
    }

    public static Result<ThingModel> checkServiceInfo(
            ThingModelEntity entity,
            CommonFetcher commonFetcher
            
    ){
        if(entity == null){
            return Result.error("物模型不存在");
        }

        ThingModel thingModel = new ThingModel();

        if(entity.getId() == null){
            return Result.error("物模型id不能为空");
        }
        thingModel.id = entity.getId();

        List<ThingModelInheritEntity> inheritEntityList = commonFetcher.list(
            "thing_model_id", entity.getId(), ThingModelInheritEntity.class
        );
        List<Long> inheritThingModelIdList = inheritEntityList.stream()
            .map(ThingModelInheritEntity::getInheritThingModelId)
            .collect(Collectors.toList());
        Result<ThingModelInherit> inheritResult = ThingModelInherit.checkInfo(
            inheritThingModelIdList,
            commonFetcher,
            THING_MODEL_ID_PATH_SPLITER + entity.getId() + THING_MODEL_ID_PATH_SPLITER
        );
        if(!inheritResult.getSignal()){
            return Result.error(inheritResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        thingModel.inherit = inheritResult.getResult();

        //继承的服务
        List<Service> inheritServices = thingModel.inherit.getServices();

        //自己的服务
        List<ThingServiceEntity> thingServiceEntities = commonFetcher.list(
            "thing_model_id", entity.getId(), ThingServiceEntity.class
        );

        if (ObjectUtil.equals(entity.getId(), Constant.DEFAULT_THING)){
            thingServiceEntities = GlobalVariables.getDefaultCommonTypeService();
        }
        List<ServiceEntityBase> serviceBases = thingServiceEntities.stream().map(t -> {
            ServiceEntityBase target = new ServiceEntityBase();
            BeanUtils.copyProperties(t, target);
            return target;
        }).collect(Collectors.toList());

        Result<List<Service>> selfServicesResult = Service.batchCheckInfo(entity.getId(),entity.getName(),serviceBases);
        if(!selfServicesResult.getSignal()){
            return Result.error(selfServicesResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        List<Service> selfServices = selfServicesResult.getResult();
        
        //检查覆盖
        Result<List<Service>> servicesResult = Service.checkOverride(
            inheritServices,
            selfServices
        );
        if(!servicesResult.getSignal()){
            return Result.error(servicesResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        List<Service> services = servicesResult.getResult();
        thingModel.services = services;
        
        return Result.ok(thingModel);
    }

    /**
     * 转成领域简单对象
     */
    public ModelDpo toDpo(){
        ModelDpo dpo = new ModelDpo();
        dpo.setId(id);
        dpo.setName(name);
        dpo.setDescript(descript);
        dpo.setModelType(modelType);
        
        if(properties != null){
            dpo.setProperties(properties.stream()
                .map(Property::toDpo)
                .collect(Collectors.toList())
            );
        }
        if(events != null){
            dpo.setEvents(events.stream()
                .map(Event::toDpo)
                .collect(Collectors.toList())
            );
        }
        if(services != null){
            dpo.setServices(services.stream()
                .map(Service::toDpo)
                .collect(Collectors.toList())
            );
        }
        if(subscriptions != null){
            dpo.setSubscriptions(subscriptions.stream()
                    .map(Subscription::toDpo)
                    .collect(Collectors.toList())
            );
        }
        return dpo;
    }

    /** 
     * 查询被继承/间接继承的所有设备模型id
     * @param thingModelId 物模型id
     * @return 被继承设备模型id列表
     */
    public static List<Long> listAllBeInheritDeviceIdById(Long thingModelId, CommonFetcher commonFetcher){
        List<Long> idList = new ArrayList<Long>();
        idList.add(thingModelId);
        idList.addAll(listAllBeInheritThingModelIdById(thingModelId, commonFetcher));
        
        return idList.stream()
            .reduce(
                new ArrayList<Long>(),
                (list, t) -> {
                    list.addAll(listBeInheritDeviceIdById(t, commonFetcher));
                    return list;
                }, (a, b) -> {
                    return null;
                }
            );

    }

    /**
     * 查询被直接继承的设备模型id
     * @param thingModelId 物模型id
     * @return 被继承设备模型id列表
     */
    private static List<Long> listBeInheritDeviceIdById(Long thingModelId, CommonFetcher commonFetcher){
        List<DeviceModelInheritEntity> entityList = commonFetcher.list(
            "inherit_thing_model_id", thingModelId, DeviceModelInheritEntity.class
        );
        return Optional.ofNullable(entityList)
            .orElse(new ArrayList<>())
            .stream()
            .map(DeviceModelInheritEntity::getDeviceId)
            .collect(Collectors.toList());
    }

    /** 
     * 查询被直接/间接继承的所有物模型id
     * @param thingModelId 物模型id
     * @return 被继承/间接继承的物模型id列表
     */
    public static List<Long> listAllBeInheritThingModelIdById(Long thingModelId, CommonFetcher commonFetcher){
        List<Long> idList = listBeInheritThingModelIdById(thingModelId, commonFetcher);
        return Optional.ofNullable(idList)
            .orElse(new ArrayList<>())
            .stream()
            .reduce(
                new ArrayList<Long>(),
                (list, t) -> {
                    list.add(t);
                    list.addAll(listAllBeInheritThingModelIdById(t, commonFetcher));
                    return list;
                }, (a, b) -> {
                    return null;
                }
            );
    }
    
    /**
     * 查询被直接继承的物模型id
     * @param thingModelId 物模型id
     * @return 被继承物模型id列表
     */
    private static List<Long> listBeInheritThingModelIdById(Long thingModelId, CommonFetcher commonFetcher){
        List<ThingModelInheritEntity> entityList = commonFetcher.list(
            "inherit_thing_model_id", thingModelId, ThingModelInheritEntity.class
        );
        return Optional.ofNullable(entityList)
            .orElse(new ArrayList<>())
            .stream()
            .map(ThingModelInheritEntity::getThingModelId)
            .collect(Collectors.toList());
    }

    /**
     * 删除校验
     */
    public static Result<String> checkDelete(Long thingModelId, CommonFetcher commonFetcher){
        List<Long> l1 = listBeInheritThingModelIdById(thingModelId, commonFetcher);
        if(l1 != null && l1.size() > 0){
            return Result.error("无法删除，被其他物模型依赖");
        }
        List<Long> l2 = listBeInheritDeviceIdById(thingModelId, commonFetcher);
        if(l2 != null && l2.size() > 0){
            return Result.error("无法删除，被其他设备模型依赖");
        }
        return Result.ok();
    }

    /**
     * 检查被继承物模型或设备模型是否正确
     * 
     */
    public Result<List<Long>> checkBeInherit(CommonFetcher commonFetcher) {
        List<Long> thingModelIds = listAllBeInheritThingModelIdById(id, commonFetcher);
        if(thingModelIds != null && thingModelIds.size() > 0){
            for(Long id:thingModelIds){
                ThingModelEntity e = commonFetcher.get(id, ThingModelEntity.class);
                if (e == null && id == -1L) {
                    e = GlobalVariables.getDefaultCommonType();
                }
                Result<ThingModel> r = ThingModel.checkInfo(e, commonFetcher);
                if(!r.getSignal()){
                    return Result.error(r.getMessage() + "，被继承物模型错误");
                }
            }
        }
        List<Long> deviceIds = listAllBeInheritDeviceIdById(id, commonFetcher);
        if(deviceIds != null && deviceIds.size() > 0){
            for(Long id:deviceIds){

                DeviceEntity e = commonFetcher.get(id, DeviceEntity.class);
                Result<DeviceModel> r = DeviceModel.checkInfo(e, commonFetcher);
                if(!r.getSignal()){
                    return Result.error(r.getMessage() + "，被继承设备模型错误");
                }
            }
        }
        return Result.ok(deviceIds);
    }

    public Result<Void> notifyDeviceNeedSync(CommonFetcher commonFetcher, CommonUpdater commonUpdater){
        List<Long> deviceIds = listAllBeInheritDeviceIdById(id, commonFetcher);

        if (deviceIds == null || deviceIds.size() <= 0) {
            return Result.ok();
        }
        for(Long deviceId:deviceIds){
            DeviceEntity deviceEntity = commonFetcher.get(deviceId, DeviceEntity.class);
            Result<Device> deviceResult = Device.checkInfoToBase(deviceEntity, commonFetcher);
            if(deviceResult.getSignal()){
                continue;
            }
            Device device = deviceResult.getResult();
            Boolean syncSuccess = device.saveNeedSync(commonUpdater);
        }
        return Result.ok();
    }

    public Set<ThingModelInheritEntity> listAllThingModelInheritEntity() {
        Set<ThingModelInheritEntity> set = new HashSet<>();
        if (CollectionUtil.isNotEmpty(this.inheritRows)) {
            set.addAll(this.inheritRows);
        }
        set.addAll(this.inherit.listAllThingModelInheritEntity());
        return set;
    }

    public Set<ThingModelEntity> listAllThingModelEntity() {
        Set<ThingModelEntity> set = new HashSet<>();
        set.add(row);
        set.addAll(this.inherit.listAllThingModelEntity());
        return set;
    }


    public static Result<ThingModel> checkInfoNew(
            ThingModelEntity entity,
            String thingModelIdPath,
            Map<Long,ThingModelEntity> thingModelEntityMap,
            Map<Long,List<ThingModelInheritEntity>> thingModelInheritEntityMap, //根据继承modelId获取到的map
            Map<Long,List<ThingServiceEntity>> thingServiceEntityMap,   //根据继承modelId获取到的map
            Map<Long,List<SubscriptionEntity>> subscriptionEntityMap    //根据继承modelId获取到的map
    ){

        if(entity == null){
            return Result.error("物模型不存在");
        }

        ThingModel thingModel = new ThingModel();

        if(entity.getId() == null){
            return Result.error("物模型id不能为空");
        }
        thingModel.id = entity.getId();
        thingModel.name = entity.getName();
        thingModel.descript = entity.getDescript();
        thingModel.modelType = entity.getModelType();
        thingModel.row = entity;

        //继承
        List<ThingModelInheritEntity> inheritEntityList = thingModelInheritEntityMap.get(entity.getId());
        if(CollectionUtil.isEmpty(inheritEntityList)){
            inheritEntityList = new ArrayList<>();
        }
        List<Long> inheritThingModelIdList = inheritEntityList.stream()
                .map(ThingModelInheritEntity::getInheritThingModelId)
                .collect(Collectors.toList());
        Result<ThingModelInherit> inheritResult = ThingModelInherit.checkInfoNew(
                inheritThingModelIdList,
                thingModelIdPath + entity.getId() + THING_MODEL_ID_PATH_SPLITER,
                thingModelEntityMap,
                thingModelInheritEntityMap,
                thingServiceEntityMap,
                subscriptionEntityMap
        );
        if(!inheritResult.getSignal()){
            return Result.error(inheritResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        thingModel.inherit = inheritResult.getResult();
        thingModel.inheritRows = inheritEntityList;
        ModelField model = entity.getModel();

        //继承的属性
        List<Property> inheritProperties = thingModel.inherit.getProperties();

        //自己的属性
        Result<List<Property>> selfPropertiesResult = Property.batchCheckInfo(
                entity.getId(), entity.getName(),
                null, null,
                model
        );
        if(!selfPropertiesResult.getSignal()){
            return Result.error(selfPropertiesResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        List<Property> selfProperties = selfPropertiesResult.getResult();

        //检查属性重复
        Result<List<Property>> propertiesResult = Property.checkRepeat(
                inheritProperties, selfProperties
        );
        if(!propertiesResult.getSignal()){
            return Result.error(propertiesResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        List<Property> properties = propertiesResult.getResult();
        thingModel.properties = properties;

        //继承的事件
        List<Event> inheritEvents = thingModel.inherit.getEvents();

        List<Property> allProperty = new ArrayList<>();
        allProperty.addAll(thingModel.properties);
        allProperty.addAll(getBaseModel().getProperties());

        //自己的事件
        Result<List<Event>> selfEventsResult = Event.batchCheckInfo(entity.getId(),entity.getName(),
                model, allProperty
        );
        if(!selfEventsResult.getSignal()){
            return Result.error(selfEventsResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        List<Event> selfEvents = selfEventsResult.getResult();

        //检查事件重复
        Result<List<Event>> eventsResult = Event.checkRepeat(
                inheritEvents, selfEvents
        );
        if(!eventsResult.getSignal()){
            return Result.error(eventsResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        thingModel.events = eventsResult.getResult();

        //继承的服务
        List<Service> inheritServices = thingModel.inherit.getServices();

        //自己的服务
        List<ThingServiceEntity> thingServiceEntities = thingServiceEntityMap.get(entity.getId());
        if(CollectionUtil.isEmpty(thingServiceEntities)){
            thingServiceEntities = new ArrayList<>();
        }
        thingServiceEntities = thingServiceEntities.stream().sorted(Comparator.comparing(ThingServiceEntity::getCreateTime).reversed()).collect(Collectors.toList());
        List<ServiceEntityBase> serviceEntities = thingServiceEntities.stream().map(t -> {
            ServiceEntityBase target = new ServiceEntityBase();
            BeanUtils.copyProperties(t, target);
            return target;
        }).collect(Collectors.toList());
        Result<List<Service>> selfServicesResult = Service.batchCheckInfo(entity.getId(),entity.getName(),serviceEntities);
        if(!selfServicesResult.getSignal()){
            return Result.error(selfServicesResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        List<Service> selfServices = selfServicesResult.getResult();

        //检查自己的物模型中是否服务重名
        Result<List<Service>> selfServicesNameCheckResult=Service.checkRepeat(selfServices);
        if(!selfServicesNameCheckResult.getSignal()){
            return Result.error(selfServicesNameCheckResult.getMessage() + ", 模型ID:" + entity.getId());
        }
        //检查服务覆盖
        Result<List<Service>> servicesResult = Service.checkOverride(
                inheritServices, selfServices
        );
        if(!servicesResult.getSignal()){
            return Result.error(servicesResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        thingModel.services = servicesResult.getResult();

        //继承的订阅
        List<Subscription> inheritSubscriptions = thingModel.inherit.getSubscriptions();

        //自己的订阅
        List<SubscriptionEntity> subscriptionEntities = subscriptionEntityMap.get(entity.getId());
        if(CollectionUtil.isEmpty(subscriptionEntities)){
            subscriptionEntities = new ArrayList<>();
        }
        subscriptionEntities = subscriptionEntities.stream().sorted(Comparator.comparing(SubscriptionEntity::getCreateTime).reversed()).collect(Collectors.toList());

        Result<List<Subscription>> selfSubscriptionsResult = Subscription.batchCheckInfo(entity.getId(),entity.getName(),subscriptionEntities);
        if(!selfSubscriptionsResult.getSignal()){
            return Result.error(selfSubscriptionsResult.getMessage() + ", thingModelId:" + entity.getId());
        }
        List<Subscription> selfSubscriptions = selfSubscriptionsResult.getResult();

        //检查自己的物模型中是否订阅重名
        Result<List<Subscription>> subscriptionResult = Subscription.checkRepeat(inheritSubscriptions,selfSubscriptions);
        if(!subscriptionResult.getSignal()){
            return Result.error(subscriptionResult.getMessage() + ", 模型ID:" + entity.getId());
        }

        thingModel.subscriptions = subscriptionResult.getResult();

        return Result.ok(thingModel);
    }
}
